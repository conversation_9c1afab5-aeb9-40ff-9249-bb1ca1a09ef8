#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 连续追问功能深度分析
基于通义千问集成的实际测试
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
from pandasai import SmartDataframe
import requests
import re

load_dotenv()

class ConversationalLLM(LLM):
    """支持连续对话的LLM类"""
    
    def __init__(self, model="qwen-plus"):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        self.conversation_history = []  # 存储对话历史
        
    def call(self, instruction, value):
        """支持上下文的对话调用"""
        # 构建包含历史上下文的提示词
        context = self.build_context()
        
        prompt = f"""你是数据分析专家，正在进行连续的数据分析对话。

{context}

当前数据信息:
{value}

用户当前查询: {instruction}

要求:
1. 考虑之前的对话上下文
2. 生成可执行的Python代码
3. 使用变量名df表示DataFrame
4. 如果是追问，要基于之前的分析结果
5. 包含适当的输出语句

Python代码:"""

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1}
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                clean_code = self.clean_code(code)
                
                # 更新对话历史
                self.add_to_history(instruction, clean_code)
                
                return clean_code
            return "print('API调用失败')"
        except Exception as e:
            return f"print('API异常: {e}')"
    
    def build_context(self):
        """构建对话上下文"""
        if not self.conversation_history:
            return "这是对话的开始。"
        
        context = "对话历史:\n"
        for i, (query, code) in enumerate(self.conversation_history[-3:], 1):  # 只保留最近3轮对话
            context += f"{i}. 用户问: {query}\n   生成代码: {code[:100]}...\n"
        
        return context
    
    def add_to_history(self, query, code):
        """添加到对话历史"""
        self.conversation_history.append((query, code))
        
        # 限制历史长度，避免上下文过长
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def clean_code(self, code):
        """清理生成的代码"""
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not any(char in line for char in '。，：；！？""''（）【】'):
                if not (line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print'])):
                    clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    @property
    def type(self):
        return "conversational_llm"

def test_basic_conversational_flow():
    """测试基本连续对话流程"""
    print("💬 基本连续对话流程测试")
    print("=" * 50)
    
    # 创建测试数据
    data = {
        '员工姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
        '部门': ['技术部', '销售部', '技术部', '人事部', '销售部', '技术部'],
        '工资': [12000, 8000, 15000, 7000, 9000, 13000],
        '工作年限': [3, 2, 5, 1, 2, 4],
        '绩效评分': [4.5, 4.2, 4.8, 4.0, 4.3, 4.6]
    }
    
    df = pd.DataFrame(data)
    print("📊 员工数据:")
    print(df)
    print()
    
    # 创建支持对话的LLM
    llm = ConversationalLLM()
    
    # 模拟连续对话场景
    conversation_flow = [
        "显示所有员工的基本信息",
        "那么平均工资是多少？",
        "工资最高的是谁？",
        "他在哪个部门？",
        "这个部门还有其他员工吗？",
        "技术部的平均工资是多少？",
        "与公司整体平均工资相比如何？"
    ]
    
    print("🔄 开始连续对话测试:")
    print("-" * 50)
    
    for i, query in enumerate(conversation_flow, 1):
        print(f"\n第{i}轮对话:")
        print(f"👤 用户: {query}")
        
        try:
            code = llm.call(query, df.to_string())
            print(f"🤖 生成代码: {code}")
            print(f"📊 执行结果:")
            exec(code)
            print("✅ 对话成功")
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
        
        print("-" * 30)

def test_context_awareness():
    """测试上下文感知能力"""
    print("\n🧠 上下文感知能力测试")
    print("=" * 50)
    
    # 创建销售数据
    sales_data = {
        '产品': ['iPhone', 'iPad', 'MacBook', 'AirPods', 'Apple Watch'],
        '销量': [1200, 800, 400, 1500, 1000],
        '价格': [6999, 4599, 14999, 1899, 3199],
        '类别': ['手机', '平板', '电脑', '配件', '配件']
    }
    
    df = pd.DataFrame(sales_data)
    print("📊 销售数据:")
    print(df)
    print()
    
    llm = ConversationalLLM()
    
    # 测试上下文相关的对话
    context_queries = [
        "计算每个产品的销售额",
        "哪个产品销售额最高？",
        "它的利润率如何？（假设成本是价格的60%）",
        "与其他同类别产品比较如何？",
        "配件类产品的整体表现怎么样？"
    ]
    
    print("🔍 上下文感知测试:")
    print("-" * 50)
    
    for i, query in enumerate(context_queries, 1):
        print(f"\n第{i}轮 - 上下文查询:")
        print(f"👤 用户: {query}")
        
        try:
            code = llm.call(query, df.to_string())
            print(f"🤖 AI理解: {code}")
            print(f"📊 结果:")
            exec(code)
            print("✅ 上下文理解正确")
            
        except Exception as e:
            print(f"❌ 上下文理解失败: {e}")
        
        print("-" * 30)

def test_smartdataframe_conversational():
    """测试SmartDataframe的对话功能"""
    print("\n🔗 SmartDataframe对话功能测试")
    print("=" * 50)
    
    # 创建测试数据
    financial_data = {
        '年份': [2019, 2020, 2021, 2022, 2023],
        '收入': [1000, 1200, 1100, 1400, 1600],
        '支出': [800, 950, 900, 1100, 1200],
        '利润': [200, 250, 200, 300, 400]
    }
    
    df = pd.DataFrame(financial_data)
    print("📊 财务数据 (单位: 万元):")
    print(df)
    print()
    
    # 创建支持对话的SmartDataframe
    llm = ConversationalLLM()
    smart_df = SmartDataframe(df, config={
        "llm": llm,
        "verbose": False,
        "conversational": True  # 启用对话模式
    })
    
    # 对话式查询序列
    conversational_queries = [
        "分析这5年的财务趋势",
        "哪一年的利润最高？",
        "那一年的利润率是多少？",
        "与前一年相比增长了多少？",
        "整体5年的平均增长率如何？"
    ]
    
    print("💬 SmartDataframe对话测试:")
    print("-" * 50)
    
    for i, query in enumerate(conversational_queries, 1):
        print(f"\n第{i}轮对话:")
        print(f"👤 用户: {query}")
        
        try:
            result = smart_df.chat(query)
            print(f"🤖 SmartDataframe: {result}")
            print("✅ 对话成功")
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
        
        print("-" * 30)

def analyze_conversational_capabilities():
    """分析连续对话能力"""
    print("\n📊 连续对话能力分析")
    print("=" * 50)
    
    print("""
🎯 PandasAI V2 连续对话功能特性:

1. 💬 对话模式支持
   ✅ conversational配置选项
   ⚠️  上下文保持能力有限
   ✅ 多轮对话支持
   ⚠️  需要自定义实现增强

2. 🧠 上下文管理
   ⚠️  默认不保存对话历史
   ✅ 可通过自定义LLM实现
   ✅ 支持引用前一轮结果
   ⚠️  长对话可能丢失上下文

3. 🔄 对话流程控制
   ✅ 支持追问和澄清
   ✅ 可处理代词引用
   ⚠️  复杂上下文理解有限
   ✅ 支持话题转换

4. 🛠️ 技术实现方式
   - 自定义LLM类扩展
   - 对话历史存储
   - 上下文构建机制
   - 智能提示词工程

5. 📈 支持的对话类型
   ✅ 数据探索对话
   ✅ 分析深入追问
   ✅ 结果解释对话
   ✅ 比较分析对话
   ✅ 趋势分析对话

6. ⚙️ 配置选项
   - conversational: True/False
   - verbose: 详细输出控制
   - 自定义历史长度
   - 上下文窗口大小

🔍 局限性分析:
- 默认对话能力较弱
- 需要额外开发支持
- 长对话容易丢失上下文
- 复杂引用关系处理困难

💡 改进建议:
- 实现专门的对话管理器
- 增强上下文理解能力
- 添加对话状态跟踪
- 支持对话历史回溯
- 实现智能上下文压缩

🚀 最佳实践:
1. 使用自定义ConversationalLLM
2. 限制对话历史长度
3. 实现关键信息提取
4. 添加对话状态管理
5. 提供上下文重置功能
""")

def demonstrate_conversational_examples():
    """演示连续对话示例"""
    print("\n💡 连续对话示例演示")
    print("=" * 50)
    
    print("""
🎯 连续对话场景示例:

场景1: 数据探索对话
👤 用户: "显示销售数据"
🤖 AI: [显示完整数据表格]
👤 用户: "哪个产品卖得最好？"
🤖 AI: "iPhone销量最高，为1200台"
👤 用户: "它的销售额是多少？"
🤖 AI: "iPhone的销售额为839.88万元"

场景2: 深入分析对话
👤 用户: "分析各部门工资情况"
🤖 AI: [显示部门工资统计]
👤 用户: "技术部平均工资多少？"
🤖 AI: "技术部平均工资13333元"
👤 用户: "比公司平均高多少？"
🤖 AI: "比公司平均工资高2666元"

场景3: 比较分析对话
👤 用户: "比较2022和2023年的业绩"
🤖 AI: [显示年度对比数据]
👤 用户: "增长率如何？"
🤖 AI: "2023年比2022年增长14.3%"
👤 用户: "主要增长来源是什么？"
🤖 AI: [分析增长构成]

🔧 实现关键点:
1. 保存对话上下文
2. 理解代词引用
3. 维护数据状态
4. 智能推理能力
5. 错误恢复机制
""")

def create_enhanced_conversational_system():
    """创建增强的对话系统示例"""
    print("\n🚀 增强对话系统示例")
    print("=" * 50)
    
    example_code = '''
class EnhancedConversationalSystem:
    """增强的对话系统"""
    
    def __init__(self, df, llm):
        self.df = df
        self.llm = llm
        self.conversation_state = {
            'current_focus': None,  # 当前关注的数据子集
            'last_result': None,    # 上一次的分析结果
            'context_variables': {} # 上下文变量
        }
    
    def chat(self, query):
        """智能对话处理"""
        # 1. 分析查询类型
        query_type = self.analyze_query_type(query)
        
        # 2. 构建增强上下文
        enhanced_context = self.build_enhanced_context(query)
        
        # 3. 生成并执行代码
        code = self.llm.call(query, enhanced_context)
        result = self.execute_with_context(code)
        
        # 4. 更新对话状态
        self.update_conversation_state(query, result)
        
        return result
    
    def analyze_query_type(self, query):
        """分析查询类型"""
        if any(word in query for word in ['它', '他', '这个', '那个']):
            return 'reference_query'
        elif any(word in query for word in ['比较', '对比', '相比']):
            return 'comparison_query'
        else:
            return 'direct_query'
    
    def build_enhanced_context(self, query):
        """构建增强上下文"""
        context = self.df.to_string()
        
        if self.conversation_state['last_result']:
            context += f"\\n上一次分析结果: {self.conversation_state['last_result']}"
        
        if self.conversation_state['current_focus']:
            context += f"\\n当前关注: {self.conversation_state['current_focus']}"
        
        return context
'''
    
    print("📝 增强对话系统代码示例:")
    print(example_code)

def main():
    """主函数"""
    print("🎯 PandasAI V2 连续对话功能深度分析")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv('DASHSCOPE_API_KEY'):
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        return
    
    # 1. 基本对话流程测试
    test_basic_conversational_flow()
    
    # 2. 上下文感知测试
    test_context_awareness()
    
    # 3. SmartDataframe对话测试
    test_smartdataframe_conversational()
    
    # 4. 能力分析
    analyze_conversational_capabilities()
    
    # 5. 示例演示
    demonstrate_conversational_examples()
    
    # 6. 增强系统示例
    create_enhanced_conversational_system()
    
    print("\n" + "=" * 60)
    print("📊 连续对话功能分析总结:")
    print("⚠️  功能完整性: 部分支持")
    print("✅ 基础对话: 支持")
    print("⚠️  上下文保持: 需要增强")
    print("✅ 自定义扩展: 完全支持")
    print("🚀 改进潜力: 巨大")

if __name__ == "__main__":
    main()
