#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 调试测试
"""

import os
import sys
import traceback

def test_step_by_step():
    """逐步测试"""
    print("=== 逐步测试 PandasAI V2 ===")
    
    try:
        print("1. 导入pandas...")
        import pandas as pd
        print("✅ pandas导入成功")
        
        print("2. 创建测试数据...")
        df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
        print("✅ 测试数据创建成功")
        
        print("3. 设置环境变量...")
        os.environ['PANDASAI_API_KEY'] = 'dummy-key-for-testing'
        print("✅ 环境变量设置成功")
        
        print("4. 导入pandasai...")
        import pandasai
        print("✅ pandasai导入成功")
        
        print("5. 导入SmartDataframe...")
        from pandasai import SmartDataframe
        print("✅ SmartDataframe导入成功")
        
        print("6. 创建SmartDataframe...")
        # 尝试不同的创建方式
        try:
            smart_df = SmartDataframe(df)
            print("✅ SmartDataframe创建成功")
            print(f"   形状: {smart_df.shape}")
            print(f"   列名: {list(smart_df.columns)}")
        except Exception as e:
            print(f"❌ SmartDataframe创建失败: {e}")
            print("尝试使用配置参数...")
            
            # 尝试使用配置
            config = {
                "llm": None,
                "verbose": False,
                "conversational": False
            }
            smart_df = SmartDataframe(df, config=config)
            print("✅ 使用配置创建SmartDataframe成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_llm_import():
    """测试LLM导入"""
    print("\n=== 测试LLM导入 ===")
    
    try:
        from pandasai.llm import OpenAI
        print("✅ OpenAI LLM导入成功")
        
        # 尝试创建LLM实例
        llm = OpenAI(api_token="dummy-key", model="gpt-3.5-turbo")
        print("✅ OpenAI LLM实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖"""
    print("\n=== 检查依赖 ===")
    
    dependencies = [
        'pandas', 'openai', 'matplotlib', 'requests', 
        'sqlalchemy', 'duckdb', 'jinja2', 'astor'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - 未安装")
        except Exception as e:
            print(f"⚠️  {dep} - 导入错误: {e}")

def show_environment_info():
    """显示环境信息"""
    print("\n=== 环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    try:
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
    except:
        print("Pandas: 未安装")
    
    try:
        import pandasai
        from pandasai.__version__ import __version__
        print(f"PandasAI版本: {__version__}")
    except:
        print("PandasAI: 版本信息不可用")

def main():
    """主函数"""
    print("PandasAI V2 详细调试测试")
    print("=" * 50)
    
    # 显示环境信息
    show_environment_info()
    
    # 检查依赖
    check_dependencies()
    
    # 逐步测试
    step_ok = test_step_by_step()
    
    # 测试LLM
    llm_ok = test_llm_import()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"基本功能: {'✅ 通过' if step_ok else '❌ 失败'}")
    print(f"LLM功能: {'✅ 通过' if llm_ok else '❌ 失败'}")
    
    if step_ok and llm_ok:
        print("\n🎉 PandasAI V2 安装验证成功!")
        print("\n可以开始使用PandasAI V2了。")
        print("参考 pandasai_v2_examples.py 了解详细用法。")
    else:
        print("\n⚠️  存在一些问题，但基本安装可能是成功的。")
        print("可能需要配置API密钥才能正常使用聊天功能。")

if __name__ == "__main__":
    main()
