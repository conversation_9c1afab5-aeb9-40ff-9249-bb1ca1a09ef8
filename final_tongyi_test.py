#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问最终集成测试 - 简化版本
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
from pandasai import SmartDataframe
import requests
import re

# 加载环境变量
load_dotenv()

class TongyiQianwenLLM(LLM):
    """通义千问LLM - 继承自PandasAI基类"""
    
    def __init__(self, api_key=None, model="qwen-plus"):
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("未找到DASHSCOPE_API_KEY")
        
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        """调用通义千问API"""
        prompt = f"""你是数据分析专家。根据以下数据和指令生成Python代码。

数据信息: {value}
用户指令: {instruction}

要求:
1. 生成可执行的Python代码
2. 使用pandas DataFrame，变量名为'df'
3. 只返回代码，不要解释
4. 确保代码正确可执行

代码:"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                code = result['choices'][0]['message']['content']
                
                # 清理代码
                code = re.sub(r'```python\n?', '', code)
                code = re.sub(r'```\n?', '', code)
                code = code.strip()
                
                return code
            else:
                return f"print('API调用失败: {response.status_code}')"
                
        except Exception as e:
            return f"print('API异常: {e}')"
    
    @property
    def type(self):
        return "tongyi_qianwen"

def test_basic_integration():
    """测试基本集成功能"""
    print("🧪 测试通义千问基本集成")
    print("=" * 40)
    
    # 1. 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到API密钥")
        return False
    
    print(f"✅ API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 2. 创建测试数据
    data = {
        '姓名': ['张三', '李四', '王五'],
        '年龄': [25, 30, 35],
        '工资': [8000, 12000, 15000]
    }
    df = pd.DataFrame(data)
    print("✅ 测试数据创建成功")
    print(df)
    
    # 3. 创建LLM
    try:
        llm = TongyiQianwenLLM()
        print("✅ 通义千问LLM创建成功")
    except Exception as e:
        print(f"❌ LLM创建失败: {e}")
        return False
    
    # 4. 创建SmartDataframe
    try:
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": False,
            "conversational": False
        })
        print("✅ SmartDataframe创建成功")
    except Exception as e:
        print(f"❌ SmartDataframe创建失败: {e}")
        return False
    
    # 5. 执行简单查询
    print("\n🔍 执行测试查询...")
    
    queries = [
        "平均工资是多少？",
        "年龄最大的是谁？",
        "工资总和是多少？"
    ]
    
    success_count = 0
    for i, query in enumerate(queries, 1):
        print(f"\n{i}. 查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   ✅ 结果: {result}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ 失败: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(queries)} 成功")
    return success_count > 0

def test_chinese_queries():
    """测试中文查询功能"""
    print("\n🇨🇳 测试中文查询功能")
    print("=" * 40)
    
    # 创建中文数据
    data = {
        '城市': ['北京', '上海', '广州', '深圳'],
        '人口': [2154, 2424, 1530, 1756],
        'GDP': [40269, 43214, 28231, 32387],
        '房价': [65000, 70000, 45000, 55000]
    }
    
    df = pd.DataFrame(data)
    print("✅ 中文数据创建成功")
    print(df)
    
    try:
        llm = TongyiQianwenLLM()
        smart_df = SmartDataframe(df, config={"llm": llm, "verbose": False})
        
        chinese_queries = [
            "哪个城市的GDP最高？",
            "人口超过2000万的城市有哪些？",
            "平均房价是多少？",
            "按GDP排序显示所有城市"
        ]
        
        success_count = 0
        for i, query in enumerate(chinese_queries, 1):
            print(f"\n{i}. 中文查询: {query}")
            try:
                result = smart_df.chat(query)
                print(f"   ✅ 结果: {result}")
                success_count += 1
            except Exception as e:
                print(f"   ❌ 失败: {e}")
        
        print(f"\n📊 中文查询结果: {success_count}/{len(chinese_queries)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 中文查询测试失败: {e}")
        return False

def create_usage_example():
    """创建使用示例文件"""
    example_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问 + PandasAI V2 使用示例
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
from pandasai import SmartDataframe
import requests
import re

# 加载环境变量
load_dotenv()

class TongyiQianwenLLM(LLM):
    """通义千问LLM"""
    
    def __init__(self, api_key=None, model="qwen-plus"):
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        prompt = f"""根据数据: {value}
指令: {instruction}
生成Python代码(使用df变量):"""

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1}
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                code = re.sub(r'```python\\n?', '', code)
                code = re.sub(r'```\\n?', '', code)
                return code.strip()
            else:
                return "print('API调用失败')"
        except:
            return "print('API异常')"
    
    @property
    def type(self):
        return "tongyi_qianwen"

# 使用示例
if __name__ == "__main__":
    # 创建数据
    df = pd.DataFrame({
        '产品': ['A', 'B', 'C'],
        '销量': [100, 200, 150],
        '价格': [10, 20, 15]
    })
    
    # 创建SmartDataframe
    llm = TongyiQianwenLLM()
    smart_df = SmartDataframe(df, config={"llm": llm})
    
    # 查询
    result = smart_df.chat("总销售额是多少？")
    print(f"结果: {result}")
'''
    
    try:
        with open('tongyi_usage_example.py', 'w', encoding='utf-8') as f:
            f.write(example_code)
        print("✅ 使用示例文件已创建: tongyi_usage_example.py")
    except Exception as e:
        print(f"❌ 创建示例文件失败: {e}")

def main():
    """主函数"""
    print("🎯 通义千问 + PandasAI V2 最终集成测试")
    print("=" * 50)
    
    # 基本集成测试
    basic_ok = test_basic_integration()
    
    if basic_ok:
        # 中文查询测试
        chinese_ok = test_chinese_queries()
        
        # 创建使用示例
        create_usage_example()
        
        print("\n" + "=" * 50)
        print("🎉 通义千问集成测试完成!")
        print(f"✅ 基本功能: {'正常' if basic_ok else '异常'}")
        print(f"✅ 中文查询: {'正常' if chinese_ok else '异常'}")
        
        if basic_ok and chinese_ok:
            print("\n🚀 集成完全成功! 现在可以使用通义千问进行数据分析了!")
            print("\n📖 使用方法:")
            print("1. 查看 tongyi_usage_example.py 了解基本用法")
            print("2. 确保.env文件中配置了DASHSCOPE_API_KEY")
            print("3. 使用中文查询获得更好的效果")
        else:
            print("\n⚠️  部分功能存在问题，但基本集成成功")
    else:
        print("\n❌ 基本集成失败，请检查配置")

if __name__ == "__main__":
    main()
