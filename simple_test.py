#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 简单测试
"""

import os
import pandas as pd

def test_installation():
    """测试PandasAI V2安装"""
    print("=== PandasAI V2 安装测试 ===")
    
    try:
        # 导入PandasAI
        import pandasai
        from pandasai import SmartDataframe
        from pandasai.llm import OpenAI
        
        print("✅ PandasAI导入成功")
        
        # 获取版本
        try:
            from pandasai.__version__ import __version__
            print(f"✅ PandasAI版本: {__version__}")
        except:
            print("⚠️  无法获取版本信息，但导入成功")
        
        # 测试基本类
        print("✅ SmartDataframe类可用")
        print("✅ OpenAI LLM类可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    try:
        # 创建测试数据
        data = {
            'Name': ['Alice', 'Bob', '<PERSON>'],
            'Age': [25, 30, 35],
            'Salary': [50000, 60000, 70000]
        }
        df = pd.DataFrame(data)
        print("✅ 测试数据创建成功")
        
        # 设置虚拟API密钥
        os.environ['PANDASAI_API_KEY'] = 'test-key'
        
        # 导入并创建SmartDataframe
        from pandasai import SmartDataframe
        
        # 不使用LLM的基本创建
        smart_df = SmartDataframe(df)
        print("✅ SmartDataframe创建成功")
        
        # 基本属性测试
        print(f"✅ 数据形状: {smart_df.shape}")
        print(f"✅ 列名: {list(smart_df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def show_configuration_examples():
    """显示配置示例"""
    print("\n=== PandasAI V2 配置示例 ===")
    
    print("""
1. OpenAI配置:
```python
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# 配置OpenAI LLM
llm = OpenAI(
    api_token="your-openai-api-key",
    model="gpt-3.5-turbo",
    temperature=0.1
)

# 创建SmartDataframe
smart_df = SmartDataframe(df, config={"llm": llm})

# 使用自然语言查询
result = smart_df.chat("What is the average salary?")
```

2. 通义千问配置 (使用OpenAI兼容接口):
```python
from pandasai.llm import OpenAI

# 配置通义千问
llm = OpenAI(
    api_token="your-dashscope-api-key",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus",
    temperature=0.1
)

smart_df = SmartDataframe(df, config={"llm": llm})
```

3. 环境变量配置:
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-key"

# 通义千问
export DASHSCOPE_API_KEY="your-dashscope-key"

# PandasAI (如果需要)
export PANDASAI_API_KEY="your-pandasai-key"
```
""")

def show_v2_vs_v3_differences():
    """显示V2和V3的差异"""
    print("\n=== PandasAI V2 vs V3 主要差异 ===")
    
    print("""
V2 (当前安装版本):
- 使用 SmartDataframe 类
- LLM通过config参数配置
- 查询: smart_df.chat("query")
- 内置支持多种LLM
- 相对简单的架构

V3 (Beta版本):
- 使用 pai.DataFrame 类
- 全局LLM配置: pai.config.set()
- 查询: df.chat("query")
- 需要安装扩展包
- 语义层 + 自然语言层架构
- 更多高级功能

推荐使用V2的原因:
✅ 稳定版本，生产环境可用
✅ 文档完整，社区支持好
✅ API相对简单易用
✅ 兼容性更好
""")

def main():
    """主函数"""
    print("PandasAI V2 安装验证")
    print("=" * 50)
    
    # 测试安装
    install_ok = test_installation()
    
    if install_ok:
        # 测试基本功能
        basic_ok = test_basic_functionality()
        
        if basic_ok:
            print("\n🎉 PandasAI V2 安装和基本功能测试通过!")
        else:
            print("\n⚠️  PandasAI V2 安装成功，但基本功能测试失败")
            print("这可能是由于缺少API密钥或其他配置问题")
    else:
        print("\n❌ PandasAI V2 安装失败")
        return
    
    # 显示配置示例
    show_configuration_examples()
    
    # 显示版本差异
    show_v2_vs_v3_differences()
    
    print("\n" + "=" * 50)
    print("安装验证完成!")
    print("\n下一步:")
    print("1. 获取LLM API密钥 (OpenAI 或 通义千问)")
    print("2. 设置环境变量或在代码中配置")
    print("3. 参考 pandasai_v2_examples.py 了解详细用法")

if __name__ == "__main__":
    main()
