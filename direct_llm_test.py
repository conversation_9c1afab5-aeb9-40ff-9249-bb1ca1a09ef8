#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试通义千问LLM功能
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re

# 加载环境变量
load_dotenv()

class TongyiQianwenLLM(LLM):
    """通义千问LLM"""
    
    def __init__(self, api_key=None, model="qwen-plus"):
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("未找到DASHSCOPE_API_KEY")
        
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        print(f"✅ 通义千问LLM初始化: {self.model}")
        
    def call(self, instruction, value):
        """调用通义千问API"""
        print(f"🔍 LLM调用 - 指令: {instruction}")
        print(f"📊 数据: {str(value)[:100]}...")
        
        prompt = f"""你是数据分析专家。根据以下数据和指令生成Python代码。

数据信息: {value}
用户指令: {instruction}

要求:
1. 生成可执行的Python代码
2. 使用pandas DataFrame，变量名为'df'
3. 只返回代码，不要解释
4. 确保代码正确可执行

代码:"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        try:
            print("📡 发送API请求...")
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                code = result['choices'][0]['message']['content']
                
                # 清理代码
                code = re.sub(r'```python\n?', '', code)
                code = re.sub(r'```\n?', '', code)
                code = code.strip()
                
                print(f"✅ API响应成功")
                print(f"📝 生成的代码:\n{code}")
                return code
            else:
                error_msg = f"API调用失败: {response.status_code}"
                print(f"❌ {error_msg}")
                return f"print('{error_msg}')"
                
        except Exception as e:
            error_msg = f"API异常: {e}"
            print(f"❌ {error_msg}")
            return f"print('{error_msg}')"
    
    @property
    def type(self):
        return "tongyi_qianwen"

def test_llm_directly():
    """直接测试LLM功能"""
    print("🧪 直接测试LLM功能")
    print("=" * 40)
    
    # 创建LLM
    try:
        llm = TongyiQianwenLLM()
    except Exception as e:
        print(f"❌ LLM创建失败: {e}")
        return False
    
    # 创建测试数据
    data = {
        '姓名': ['张三', '李四', '王五'],
        '年龄': [25, 30, 35],
        '工资': [8000, 12000, 15000]
    }
    df = pd.DataFrame(data)
    
    # 直接调用LLM
    print("\n🔍 直接调用LLM...")
    instruction = "计算平均工资"
    value = df.to_string()
    
    try:
        result = llm.call(instruction, value)
        print(f"\n✅ LLM调用成功!")
        print(f"返回结果: {result}")
        
        # 尝试执行生成的代码
        print("\n🚀 执行生成的代码...")
        try:
            exec(result)
            print("✅ 代码执行成功!")
            return True
        except Exception as e:
            print(f"❌ 代码执行失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ LLM调用失败: {e}")
        return False

def test_multiple_queries():
    """测试多个查询"""
    print("\n🔄 测试多个查询")
    print("=" * 40)
    
    llm = TongyiQianwenLLM()
    
    # 测试数据
    data = {
        '产品': ['A', 'B', 'C', 'D'],
        '销量': [100, 200, 150, 300],
        '价格': [10, 20, 15, 25]
    }
    df = pd.DataFrame(data)
    df_str = df.to_string()
    
    queries = [
        "计算总销售额（销量×价格）",
        "找出销量最高的产品",
        "计算平均价格",
        "显示所有产品信息"
    ]
    
    success_count = 0
    for i, query in enumerate(queries, 1):
        print(f"\n{i}. 查询: {query}")
        try:
            result = llm.call(query, df_str)
            print(f"✅ 生成代码成功")
            
            # 尝试执行
            try:
                exec(result)
                print("✅ 代码执行成功")
                success_count += 1
            except Exception as e:
                print(f"❌ 代码执行失败: {e}")
                
        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(queries)} 成功")
    return success_count > 0

def create_simple_integration():
    """创建简单集成示例"""
    print("\n📝 创建简单集成示例")
    print("=" * 40)
    
    example_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问简单集成示例
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re

load_dotenv()

class TongyiQianwenLLM(LLM):
    def __init__(self, model="qwen-plus"):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        prompt = f"数据: {value}\\n指令: {instruction}\\n生成Python代码:"
        
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1}
        
        response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            code = response.json()['choices'][0]['message']['content']
            code = re.sub(r'```python\\n?', '', code)
            code = re.sub(r'```\\n?', '', code)
            return code.strip()
        return "print('API调用失败')"
    
    @property
    def type(self):
        return "tongyi_qianwen"

# 使用示例
def analyze_data(df, query):
    """分析数据"""
    llm = TongyiQianwenLLM()
    code = llm.call(query, df.to_string())
    print(f"生成的代码:\\n{code}")
    exec(code)

# 示例使用
if __name__ == "__main__":
    df = pd.DataFrame({
        '产品': ['iPhone', 'iPad', 'MacBook'],
        '销量': [1000, 800, 500],
        '价格': [6999, 4599, 14999]
    })
    
    analyze_data(df, "计算总销售额")
'''
    
    try:
        with open('simple_tongyi_integration.py', 'w', encoding='utf-8') as f:
            f.write(example_code)
        print("✅ 简单集成示例已创建: simple_tongyi_integration.py")
    except Exception as e:
        print(f"❌ 创建示例失败: {e}")

def main():
    """主函数"""
    print("🎯 通义千问LLM直接测试")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY")
        return
    
    print(f"✅ API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 直接测试LLM
    basic_ok = test_llm_directly()
    
    if basic_ok:
        # 测试多个查询
        multiple_ok = test_multiple_queries()
        
        # 创建简单集成示例
        create_simple_integration()
        
        print("\n" + "=" * 50)
        print("🎉 通义千问LLM测试完成!")
        print(f"✅ 基本功能: {'正常' if basic_ok else '异常'}")
        print(f"✅ 多查询测试: {'正常' if multiple_ok else '异常'}")
        
        if basic_ok:
            print("\n🚀 LLM功能正常! 可以生成和执行代码!")
            print("📖 查看 simple_tongyi_integration.py 了解使用方法")
        
    else:
        print("\n❌ LLM基本功能测试失败")

if __name__ == "__main__":
    main()
