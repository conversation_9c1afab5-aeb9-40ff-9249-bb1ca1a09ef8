#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问连接测试和验证
"""

import os
import sys
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from dotenv import load_dotenv
import requests
import json

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 加载.env文件
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("请确保在.env文件中配置了DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ 找到API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 检查必要的包
    required_packages = ['pandas', 'pandasai', 'python-dotenv', 'requests']
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            return False
    
    return True

def test_api_direct():
    """直接测试通义千问API"""
    print("\n🔗 直接测试通义千问API...")
    
    api_key = os.getenv('DASHSCOPE_API_KEY')
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "qwen-plus",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, please respond with 'API connection successful'"}
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ API直接调用成功")
            print(f"   响应: {content}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False

def test_pandasai_llm():
    """测试PandasAI LLM配置"""
    print("\n🤖 测试PandasAI LLM配置...")
    
    try:
        api_key = os.getenv('DASHSCOPE_API_KEY')
        
        # 创建LLM实例
        llm = OpenAI(
            api_token=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            model="qwen-plus",
            temperature=0.1,
            max_tokens=1000
        )
        
        print("✅ LLM实例创建成功")
        return llm
        
    except Exception as e:
        print(f"❌ LLM实例创建失败: {e}")
        return None

def test_smartdataframe():
    """测试SmartDataframe功能"""
    print("\n📊 测试SmartDataframe功能...")
    
    # 获取LLM实例
    llm = test_pandasai_llm()
    if not llm:
        return False
    
    try:
        # 创建测试数据
        test_data = {
            '城市': ['北京', '上海', '广州', '深圳', '杭州'],
            '人口': [2154, 2424, 1530, 1756, 1220],
            'GDP': [40269, 43214, 28231, 32387, 18109],
            '平均工资': [12000, 13500, 9800, 11200, 10500]
        }
        
        df = pd.DataFrame(test_data)
        print("✅ 测试数据创建成功")
        print(df)
        
        # 创建SmartDataframe
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": False,
            "conversational": False
        })
        
        print("✅ SmartDataframe创建成功")
        
        # 执行测试查询
        test_queries = [
            "哪个城市的GDP最高？",
            "计算平均工资的平均值",
            "人口超过2000万的城市有哪些？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n测试查询 {i}: {query}")
            try:
                result = smart_df.chat(query)
                print(f"✅ 查询成功: {result}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ SmartDataframe测试失败: {e}")
        return False

def test_different_models():
    """测试不同的通义千问模型"""
    print("\n🔄 测试不同的通义千问模型...")
    
    models = ['qwen-turbo', 'qwen-plus', 'qwen-max']
    api_key = os.getenv('DASHSCOPE_API_KEY')
    
    for model in models:
        print(f"\n测试模型: {model}")
        try:
            llm = OpenAI(
                api_token=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                model=model,
                temperature=0.1,
                max_tokens=500
            )
            
            # 简单测试
            test_df = pd.DataFrame({'数字': [1, 2, 3], '值': [10, 20, 30]})
            smart_df = SmartDataframe(test_df, config={"llm": llm, "verbose": False})
            
            result = smart_df.chat("计算值列的总和")
            print(f"✅ {model} 测试成功: {result}")
            
        except Exception as e:
            print(f"❌ {model} 测试失败: {e}")

def generate_config_template():
    """生成配置模板"""
    print("\n📝 生成配置模板...")
    
    config_template = """# 通义千问配置模板

## 1. 环境变量配置 (.env文件)
DASHSCOPE_API_KEY=your-dashscope-api-key-here

## 2. Python代码配置
```python
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

# 配置通义千问LLM
llm = OpenAI(
    api_token=os.getenv('DASHSCOPE_API_KEY'),
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus",  # 可选: qwen-turbo, qwen-plus, qwen-max
    temperature=0.1,
    max_tokens=2000
)

# 创建SmartDataframe
smart_df = SmartDataframe(df, config={"llm": llm})

# 使用中文查询
result = smart_df.chat("分析数据的趋势")
```

## 3. 支持的模型
- qwen-turbo: 快速响应，适合简单查询
- qwen-plus: 平衡性能，推荐使用
- qwen-max: 最强性能，适合复杂分析

## 4. 配置参数
- temperature: 0.1 (推荐用于数据分析)
- max_tokens: 1000-2000 (根据需要调整)
- base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
"""
    
    try:
        with open('tongyi_config_template.md', 'w', encoding='utf-8') as f:
            f.write(config_template)
        print("✅ 配置模板已保存到 tongyi_config_template.md")
    except Exception as e:
        print(f"❌ 保存配置模板失败: {e}")

def main():
    """主函数"""
    print("🧪 通义千问连接测试和验证")
    print("=" * 50)
    
    # 1. 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请先配置环境")
        return
    
    # 2. 直接测试API
    if not test_api_direct():
        print("\n❌ API直接测试失败，请检查API密钥")
        return
    
    # 3. 测试PandasAI集成
    if not test_smartdataframe():
        print("\n❌ PandasAI集成测试失败")
        return
    
    # 4. 测试不同模型
    test_different_models()
    
    # 5. 生成配置模板
    generate_config_template()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成!")
    print("\n✅ 测试结果:")
    print("- 环境配置正确")
    print("- API连接正常")
    print("- PandasAI集成成功")
    print("- 支持中文查询")
    print("- 多模型可用")
    
    print("\n🚀 现在可以使用 tongyi_qianwen_integration.py 进行完整演示!")

if __name__ == "__main__":
    main()
