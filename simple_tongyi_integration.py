#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问简单集成示例
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re

load_dotenv()

class TongyiQianwenLLM(LLM):
    def __init__(self, model="qwen-plus"):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        prompt = f"""根据以下数据和指令，生成可执行的Python代码。

数据: {value}
指令: {instruction}

要求:
1. 只返回Python代码，不要任何解释
2. 使用变量名df表示DataFrame
3. 代码必须可以直接执行
4. 不要包含创建DataFrame的代码

Python代码:"""

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1}

        response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            code = response.json()['choices'][0]['message']['content']

            # 更强的代码清理
            code = re.sub(r'```python\n?', '', code)
            code = re.sub(r'```\n?', '', code)

            # 移除中文解释文字
            lines = code.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                # 跳过中文解释行和空行
                if line and not any(char in line for char in '。，：；！？""''（）【】'):
                    # 跳过以中文开头的行
                    if not (line and ord(line[0]) > 127):
                        clean_lines.append(line)

            code = '\n'.join(clean_lines).strip()
            return code if code else "print('代码生成失败')"
        return "print('API调用失败')"
    
    @property
    def type(self):
        return "tongyi_qianwen"

# 使用示例
def analyze_data(df, query):
    """分析数据"""
    llm = TongyiQianwenLLM()
    code = llm.call(query, df.to_string())
    print(f"生成的代码:\n{code}")
    exec(code)

# 示例使用
if __name__ == "__main__":
    df = pd.DataFrame({
        '产品': ['iPhone', 'iPad', 'MacBook'],
        '销量': [1000, 800, 500],
        '价格': [6999, 4599, 14999]
    })
    
    analyze_data(df, "计算总销售额")
